<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="./images/boy.png"/>
        <h5 class="mt-3">
         Shubhrit
        </h5>
        <p>
         Frontend Engineer
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Go to amazon.in and look for the best clothes to buy for a wedding
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Find the best clothes options for a wedding on Amazon India."/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Efficiently navigate the Amazon India website, search for wedding clothes, and evaluate top options based on search results."/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Entry
     </div>
     <div class="stage" style="background-color: #F55050">
      Search
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Selection
     </div>
     <div class="stage" style="background-color: #F7C934">
      Evaluation
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Decision
     </div>
     <div class="stage" style="background-color: #5E2590">
      Report
     </div>
    </div>
   </div>
   <div class="container mt-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Opened browser and navigated to Amazon India website.
     </div>
     <div class="stage" style="background-color: #F55050">
      Searched for 'wedding clothes' in the search box.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Chose 'wedding clothes for women' from suggestions and submitted search.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Evaluated top options for wedding clothes from search results.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Scrolled down the page to review more options.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Completed task by reporting the best wedding clothes options based on the search results.
     </div>
    </div>
   </div>
   <div class="container mt-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Neutral
     </div>
     <div class="stage" style="background-color: #F55050">
      Focused
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Determined
     </div>
     <div class="stage" style="background-color: #F7C934">
      Inquisitive
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Concentrated
     </div>
     <div class="stage" style="background-color: #5E2590">
      Satisfied
     </div>
    </div>
   </div>
   <div class="container mt-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      No significant obstacles; navigation was straightforward.
     </div>
     <div class="stage" style="background-color: #F55050">
      Need to ensure correct keyword input and choose the appropriate category.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Potentially too many options; need to narrow down the search results for the best options.
     </div>
     <div class="stage" style="background-color: #F7C934">
      High volume of search results; sorted through to find eligible products.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Overwhelming number of choices; need to filter based on quality, price, and reviews.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Ensuring the accuracy of the report and summarizing the key findings.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
