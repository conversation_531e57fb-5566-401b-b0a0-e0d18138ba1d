 SystemMessage 
You are a precise browser automation agent that interacts with websites through structured commands. Your role is to:
1. Analyze the provided webpage elements and structure
2. Plan a sequence of actions to accomplish the given task
3. Respond with valid JSON containing your action sequence and state assessment

Current date and time: 2025-02-13 01:57


INPUT STRUCTURE:
1. Current URL: The webpage you're currently on
2. Available Tabs: List of open browser tabs
3. Interactive Elements: List in the format:
   index[:]<element_type>element_text</element_type>
   - index: Numeric identifier for interaction
   - element_type: HTML element type (button, input, etc.)
   - element_text: Visible text or element description

Example:
33[:]<button>Submit Form</button>
_[:] Non-interactive text


Notes:
- Only elements with numeric indexes are interactive
- _[:] elements provide context but cannot be interacted with



1. RESPONSE FORMAT: You must ALWAYS respond with valid JSON in this exact format:
   {
     "current_state": {
       "evaluation_previous_goal": "Success|Failed|Unknown - Analyze the current elements and the image to check if the previous goals/actions are successful like intended by the task. Ignore the action result. The website is the ground truth. Also mention if something unexpected happened like new suggestions in an input field. Shortly state why/why not",
       "memory": "Description of what has been done and what you need to remember until the end of the task",
       "next_goal": "What needs to be done with the next actions"
     },
     "action": [
       {
         "one_action_name": {
           // action-specific parameter
         }
       },
       // ... more actions in sequence
     ]
   }

2. ACTIONS: You can specify multiple actions in the list to be executed in sequence. But always specify only one action name per item.

   Common action sequences:
   - Form filling: [
       {"input_text": {"index": 1, "text": "username"}},
       {"input_text": {"index": 2, "text": "password"}},
       {"click_element": {"index": 3}}
     ]
   - Navigation and extraction: [
       {"open_new_tab": {}},
       {"go_to_url": {"url": "https://example.com"}},
       {"extract_page_content": {}}
     ]


3. ELEMENT INTERACTION:
   - Only use indexes that exist in the provided element list
   - Each element has a unique index number (e.g., "33[:]<button>")
   - Elements marked with "_[:]" are non-interactive (for context only)

4. NAVIGATION & ERROR HANDLING:
   - If no suitable elements exist, use other functions to complete the task
   - If stuck, try alternative approaches
   - Handle popups/cookies by accepting or closing them
   - Use scroll to find elements you are looking for

5. TASK COMPLETION:
   - Use the done action as the last action as soon as the task is complete
   - Don't hallucinate actions
   - If the task requires specific information - make sure to include everything in the done function. This is what the user will see.
   - If you are running out of steps (current step), think about speeding it up, and ALWAYS use the done action as the last action.

6. VISUAL CONTEXT:
   - When an image is provided, use it to understand the page layout
   - Bounding boxes with labels correspond to element indexes
   - Each bounding box and its label have the same color
   - Most often the label is inside the bounding box, on the top right
   - Visual context helps verify element locations and relationships
   - sometimes labels overlap, so use the context to verify the correct element

7. Form filling:
   - If you fill an input field and your action sequence is interrupted, most often a list with suggestions popped up under the field and you need to first select the right element from the suggestion list.

8. ACTION SEQUENCING:
   - Actions are executed in the order they appear in the list
   - Each action should logically follow from the previous one
   - If the page changes after an action, the sequence is interrupted and you get the new state.
   - If content only disappears the sequence continues.
   - Only provide the action sequence until you think the page will change.
   - Try to be efficient, e.g. fill forms at once, or chain actions where nothing changes on the page like saving, extracting, checkboxes...
   - only use multiple actions if it makes sense.


   - use maximum 10 actions per sequence

Functions:
Complete task: 
{done: {'text': {'type': 'string'}}}
Search Google in the current tab: 
{search_google: {'query': {'type': 'string'}}}
Navigate to URL in the current tab: 
{go_to_url: {'url': {'type': 'string'}}}
Go back: 
{go_back: {}}
Click element: 
{click_element: {'index': {'type': 'integer'}, 'xpath': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None}}}
Input text into a input interactive element: 
{input_text: {'index': {'type': 'integer'}, 'text': {'type': 'string'}, 'xpath': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None}}}
Switch tab: 
{switch_tab: {'page_id': {'type': 'integer'}}}
Open url in new tab: 
{open_tab: {'url': {'type': 'string'}}}
Extract page content to get the pure text or markdown with links if include_links is set to true: 
{extract_content: {'include_links': {'type': 'boolean'}}}
Scroll down the page by pixel amount - if no amount is specified, scroll down one page: 
{scroll_down: {'amount': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None}}}
Scroll up the page by pixel amount - if no amount is specified, scroll up one page: 
{scroll_up: {'amount': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None}}}
Send strings of special keys like Backspace, Insert, PageDown, Delete, Enter, Shortcuts such as `Control+o`, `Control+Shift+T` are supported as well. This gets used in keyboard.press. Be aware of different operating systems and their shortcuts: 
{send_keys: {'keys': {'type': 'string'}}}
If you dont find something which you want to interact with, scroll to it: 
{scroll_to_text: {'text': {'type': 'string'}}}
Get all options from a native dropdown: 
{get_dropdown_options: {'index': {'type': 'integer'}}}
Select dropdown option for interactive element index by the text of the option you want to select: 
{select_dropdown_option: {'index': {'type': 'integer'}, 'text': {'type': 'string'}}}
Ask user for login username and password: 
{ask_human: {'question': {'type': 'string'}}}

Remember: Your responses must be valid JSON matching the specified format. Each action in the sequence must be valid.

 HumanMessage 
Your ultimate task is: Go to amazon.com and search for  samsung mobile phones under 10000. If you achieved your ultimate task, stop everything and use the done action in the next step to complete the task. If not, continue as usual.

 AIMessage 


 ToolMessage 
Browser started

 HumanMessage 
Current url: about:blank
Available tabs:
[TabInfo(page_id=0, url='about:blank', title='')]
Interactive elements from current page view:
empty page

 RESPONSE
{
  "current_state": {
    "evaluation_previous_goal": "Success - The browser started successfully.",
    "memory": "",
    "next_goal": "Go to Amazon's website"
  },
  "action": [
    {
      "go_to_url": {
        "url": "https://www.amazon.com"
      }
    }
  ]
}